<?php
/**
 * Services Page - Monolith Design Co.
 * Showcases all 9 services offered by the company
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get services data from database
$services = getServices();

// Page configuration
$page_title = 'Our Services - Monolith Design Co.';
$page_description = 'Comprehensive architectural and construction services including design, engineering, project management, and sustainable solutions.';
$page_keywords = 'architectural services, construction management, structural engineering, sustainable design, project planning';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">
    
    <!-- Google Fonts - Arkify uses Public Sans and Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">

    <!-- Custom accent color -->
    <style>
        :root {
            --accent-color: <?php echo getThemeOption('accent_color', '#E67E22'); ?>;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>
    
    <!-- Main Content -->
    <main>
        <!-- Section 1: Page Hero -->
        <?php
        // Hero data for services page
        $hero_title = 'Our Services';
        $hero_subtitle = 'Comprehensive Architectural & Construction Solutions';
        $hero_background = 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1600&h=900&fit=crop'; // Modern office building background
        $hero_overlay_class = 'hero-faded-overlay';
        $breadcrumb_items = [
            ['title' => 'Home', 'url' => 'index.php'],
            ['title' => 'Services', 'url' => '']
        ];
        
        // Load hero template
        include 'templates/page-hero.php';
        ?>
        
        <!-- Section 2: Services Grid -->
        <section class="services-grid section-padding">
            <div class="container">
                <div class="section-header">
                    <span class="section-caption">[ Our Services ]</span>
                    <h2 class="section-title">Our Services</h2>
                </div>
                
                <ul class="services-list">
                    <?php if (!empty($services)): ?>
                        <?php foreach ($services as $service): ?>
                            <li class="service-item">
                                <a href="service-details?service=<?php echo urlencode($service['slug']); ?>" class="service-link">
                                    <div class="service-icon">
                                        <?php
                                        // Icon mapping for services
                                        $icon_map = [
                                            'architectural-design' => 'architecture.svg',
                                            'structural-engineering' => 'engineering.svg',
                                            'construction-management' => 'construction.svg',
                                            'sustainable-design' => 'sustainable.svg',
                                            'interior-design' => 'interior.svg',
                                            'project-management' => 'project-management.svg',
                                            'urban-planning' => 'urban-planning.svg',
                                            'renovation-restoration' => 'renovation.svg',
                                            'consulting-advisory' => 'consulting.svg'
                                        ];

                                        $icon_file = isset($icon_map[$service['slug']]) ? $icon_map[$service['slug']] : 'architecture.svg';
                                        ?>
                                        <img src="assets/images/icons/<?php echo $icon_file; ?>" alt="<?php echo htmlspecialchars($service['title']); ?>" width="48" height="48">
                                    </div>
                                    <div class="service-content">
                                        <h3 class="service-title"><?php echo htmlspecialchars($service['title']); ?></h3>
                                        <p class="service-description">
                                            <?php echo htmlspecialchars($service['description']); ?>
                                        </p>
                                    </div>
                                    <div class="service-action">
                                        <span class="service-button">View Details</span>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M7 17L17 7M17 7H7M17 7V17"/>
                                        </svg>
                                    </div>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li class="service-item">
                            <a href="service-details?service=architectural-design" class="service-link">
                                <div class="service-icon">
                                    <img src="assets/images/icons/architecture.svg" alt="Architectural Design" width="48" height="48">
                                </div>
                                <div class="service-content">
                                    <h3 class="service-title">Architectural Design</h3>
                                    <p class="service-description">
                                        Creative and functional design solutions that balance aesthetics with practicality for residential and commercial projects.
                                    </p>
                                </div>
                                <div class="service-action">
                                    <span class="service-button">View Details</span>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M7 17L17 7M17 7H7M17 7V17"/>
                                    </svg>
                                </div>
                            </a>
                        </li>
                    <?php endif; ?>

                </ul>
            </div>
        </section>

        <!-- Section 3: CTA Section -->
        <section class="cta-section section-padding">
            <div class="container">
                <div class="cta-content">
                    <div class="cta-text">
                        <span class="section-caption">Ready to Start?</span>
                        <h2 class="section-title">Let's Discuss Your Project</h2>
                    </div>
                    <p class="cta-description">Ready to bring your vision to life? Our team of experienced professionals is here to guide you through every step of your construction journey.</p>
                    <a href="contact.php" class="btn btn-primary">
                        GET STARTED TODAY
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 17L17 7M17 7H7M17 7V17"/>
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>
    
    <!-- Scripts -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
