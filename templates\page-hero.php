<?php
/**
 * Page Hero Template
 * Reusable hero section for all pages except home
 * 
 * Variables expected:
 * $hero_title - Main page title
 * $hero_subtitle - Optional subtitle
 * $hero_background - Background image URL
 * $breadcrumb_items - Array of breadcrumb items
 */

// Default values
$hero_title = $hero_title ?? 'Page Title';
$hero_subtitle = $hero_subtitle ?? '';
$hero_background = $hero_background ?? 'assets/images/demo-image/demo-images/imgi_42_678b4fa8626623b854fc160e_project-main01-p-800.jpg';
$breadcrumb_items = $breadcrumb_items ?? [
    ['title' => 'Home', 'url' => 'index.php'],
    ['title' => 'Page', 'url' => '']
];
?>

<!-- Page Hero Section -->
<section class="page-hero" style="background-image: url('<?php echo $hero_background; ?>');">
    <div class="hero-content">
        <div class="breadcrumb">
            <?php foreach ($breadcrumb_items as $index => $item): ?>
                <?php if ($index > 0): ?>
                    <span>/</span>
                <?php endif; ?>
                
                <?php if (!empty($item['url'])): ?>
                    <a href="<?php echo $item['url']; ?>"><?php echo htmlspecialchars($item['title']); ?></a>
                <?php else: ?>
                    <span><?php echo htmlspecialchars($item['title']); ?></span>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <h1 class="hero-title"><?php echo htmlspecialchars($hero_title); ?></h1>
        
        <?php if (!empty($hero_subtitle)): ?>
            <p class="hero-subtitle"><?php echo htmlspecialchars($hero_subtitle); ?></p>
        <?php endif; ?>
    </div>
</section>
