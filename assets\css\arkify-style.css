/**
 * Arkify-Inspired Styles for Monolith Design Co.
 * Based on the Arkify template design
 */

/* CSS Variables for Arkify Design System */
:root {
    /* Colors - Updated to match admin config (Orange, Black, Blue) */
    --primary-color: #1A1A1A; /* Black from admin config */
    --primary-color-rgb: 26, 26, 26; /* RGB values for primary color */
    --primary-dark: #0F0F0F; /* Darker version of primary color */
    --secondary-color: #F5F5F5; /* Light gray background */
    --accent-color: #E67E22; /* Orange from admin config */
    --blue-accent: #3498DB; /* Blue accent color */
    --text-color: #1A1A1A;
    --text-light: #666666;
    --white: #FFFFFF;
    --black: #000000;
    --light-accent: #FDF6F0; /* Light warm accent background */
    --border-color: #E5E5E5;
    
    /* Typography - Arkify uses Public Sans and Inter */
    --font-heading: 'Public Sans', sans-serif;
    --font-body: 'Inter', sans-serif;
    
    /* Spacing */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    --spacing-xxl: 64px;
    --spacing-xxxl: 96px;
    
    /* Container */
    --container-max-width: 1400px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
    
    /* Border Radius */
    --radius-small: 4px;
    --radius-medium: 8px;
    --radius-large: 16px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 600;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
}

h5 {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
}

h6 {
    font-size: 1rem;
    font-weight: 600;
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-light);
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-white {
    color: var(--white);
}

.bg-light-accent {
    background-color: var(--light-accent);
}

.bg-primary {
    background-color: var(--primary-color);
}

.d-none {
    display: none;
}

/* Buttons */
.primary-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-medium);
    font-weight: 500;
    transition: all var(--transition-medium);
    border: 2px solid var(--primary-color);
}

.primary-button:hover {
    background-color: transparent;
    color: var(--primary-color);
    transform: translateY(-2px);
}

.primary-button-dark {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

.primary-button-dark:hover {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    /* Account for fixed header to center content properly */
    padding: 90px 0 40px 0;
}

/* Page Hero Section (for all pages except home) */
.page-hero {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    margin-top: 80px; /* Account for fixed header */
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
    z-index: 1;
}

/* Faded gradient overlay variant for better background visibility */
.page-hero.hero-faded-overlay::before {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.3) 100%);
}

.page-hero .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #ffffff; /* Ensure pure white */
    max-width: 800px;
    padding: 0 var(--spacing-md);
}

.page-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff; /* Ensure pure white */
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.5);
}

.page-hero .hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.95;
    margin-bottom: 2rem;
    color: #ffffff; /* Ensure pure white */
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7), 0 0 15px rgba(0, 0, 0, 0.4);
}

.page-hero .breadcrumb {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
    color: #ffffff; /* Ensure pure white */
}

.page-hero .breadcrumb a {
    color: #ffffff; /* Ensure pure white */
    text-decoration: none;
    opacity: 0.8;
    transition: opacity var(--transition-fast);
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

.page-hero .breadcrumb a:hover {
    opacity: 1;
}

.page-hero .breadcrumb span {
    margin: 0 0.5rem;
    opacity: 0.7;
    color: #ffffff; /* Ensure pure white */
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-background img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 2s ease-in-out;
}

.hero-background img.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.7) 100%);
    z-index: 2;
}

.hero-section.loading .hero-overlay {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.hero-outer {
    width: 100%;
    position: relative;
    z-index: 3;
    /* Center the content perfectly accounting for header */
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 90px);
}

.hero-wrap {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 3;
    /* Remove any unwanted margins */
    padding: 0;
}

/* Hero Text Slider */
.hero-text-slider {
    position: relative;
    width: 100%;
    height: auto;
    overflow: hidden;
}

.hero-text-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-in-out;
    pointer-events: none;
}

.hero-text-slide.active {
    position: relative;
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--white);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.8);
    animation: slideInFromLeft 1s ease-out;
}

.hero-text-slide.active .hero-title {
    animation: slideInFromLeft 1s ease-out;
}

.hero-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    animation: slideInFromRight 1s ease-out 0.3s both;
}

.hero-text-slide.active .hero-info {
    animation: slideInFromRight 1s ease-out 0.3s both;
}

/* Hero Slider Navigation */
.hero-slider-nav {
    margin-top: var(--spacing-xl);
    display: flex;
    justify-content: center;
}

.slider-dots {
    display: flex;
    gap: 12px;
    align-items: center;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(26, 26, 26, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dot:hover {
    background: rgba(26, 26, 26, 0.5);
    transform: scale(1.1);
}

.dot.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.2);
    box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.2);
}

.hero-data {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    color: var(--white);
    max-width: 600px;
    line-height: 1.6;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
    font-weight: 400;
}

.hero-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 16px 32px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all var(--transition-medium);
    animation: slideInFromBottom 1s ease-out 0.6s both;
    border: 2px solid var(--primary-color);
    text-shadow: none;
}

.hero-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(50px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* About Section */
.about-section {
    padding: var(--spacing-xxxl) 0;
}

.about-wrap {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.about-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.subtitle {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--text-light);
}

.caption {
    color: var(--primary-color);
}

.about-cover,
.about-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-large);
}

.cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform var(--transition-slow);
    display: block;
    border: none;
    outline: none;
}

.cover-image:hover {
    transform: scale(1.05);
}

.about-middle {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.about-info h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.about-info p {
    font-size: 1.125rem;
    line-height: 1.7;
}

/* Section Titles */
.section-title {
    margin-bottom: var(--spacing-xl);
}

.section-title .subtitle {
    margin-bottom: var(--spacing-sm);
}

.section-title h2 {
    max-width: 600px;
}

/* Featured Projects Section */
.featured-projects-section {
    padding: var(--spacing-xxxl) 0;
}

.work-outer-main {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.work-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.work-main-card {
    position: relative;
    border-radius: var(--radius-large);
    overflow: hidden;
    cursor: pointer;
}

.work-main {
    aspect-ratio: 16/10;
    overflow: hidden;
    position: relative;
    background-color: #f5f5f5;
    height: 400px;
    width: 100%;
    border-radius: var(--radius-large);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform var(--transition-slow);
}

.work-main:hover {
    transform: scale(1.02);
}

.work-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-lg);
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: var(--white);
}

.work-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.work-mini-card {
    background-color: rgba(255,255,255,0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    backdrop-filter: blur(10px);
}

.body-small {
    font-size: 0.75rem;
    font-weight: 500;
}

.work-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.work-card {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    cursor: pointer;
    background: rgba(255, 255, 255, 0.5);
}

.work-card:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.work-cover {
    aspect-ratio: 4/3;
    border-radius: var(--radius-small);
    overflow: hidden;
    position: relative;
    background-color: #f5f5f5;
    height: 180px;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform var(--transition-slow);
}

.work-cover:hover {
    transform: scale(1.05);
}

.work-last {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--secondary-color);
    border-radius: var(--radius-large);
    margin-top: var(--spacing-lg);
}

.work-last p {
    max-width: 60%;
    margin: 0;
}

/* Statistics Section */
.statistics-section {
    padding: var(--spacing-xxxl) 0;
    color: var(--white);
}

.best-wrap {
    margin-bottom: var(--spacing-xxxl);
}

.subtitle-white {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.subtitle-white div {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(255,255,255,0.8);
}

.best-inner {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    text-align: center;
}

.best-card {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.best-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    transition: all var(--transition-medium);
}

.best-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.best-icon img {
    width: 30px;
    height: 30px;
    filter: brightness(0) invert(1);
    opacity: 0.9;
}

.best-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 700;
    margin-bottom: 0;
    color: var(--white);
}

.body-large {
    font-size: 1.25rem;
    color: rgba(255,255,255,0.9);
}

/* Client Logos Section */
.client-logos-section {
    width: 100%;
    padding: var(--spacing-lg) 0;
}

.client-logos-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
    justify-items: center;
}

.logo-card {
    flex-shrink: 0;
    width: 120px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255,255,255,0.1);
    border-radius: var(--radius-medium);
    backdrop-filter: blur(10px);
    transition: all var(--transition-medium);
}

.logo-card:hover {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.logo-card img {
    max-width: 80px;
    max-height: 40px;
    filter: brightness(0) invert(1);
    opacity: 0.7;
    transition: opacity var(--transition-medium);
}

.logo-card:hover img {
    opacity: 1;
}



/* Services Section */
.services-section {
    padding: var(--spacing-xxxl) 0;
}

.service-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxxl);
    align-items: center;
}

.project-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.service-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.service-mini-card {
    padding: var(--spacing-md);
    border: 1px solid rgba(45, 90, 39, 0.2);
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    cursor: pointer;
}

.service-mini-card:hover,
.service-mini-card.active {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
    transform: translateX(10px);
}

.service-mini-card.active {
    background-color: var(--primary-color);
    color: white;
}

.service-mini-card.active h5 {
    color: white;
}

.service-mini-card h5 {
    margin: 0;
    color: var(--text-color);
}

.service-image {
    position: relative;
    border-radius: var(--radius-large);
    overflow: hidden;
    aspect-ratio: 4/3;
}

/* Testimonials Section */
.testimonials-section {
    padding: var(--spacing-xxxl) 0;
    position: relative;
    background-color: var(--light-accent);
}

.testimonials-slider {
    position: relative;
}

.testimonial-slide {
    display: none;
}

.testimonial-slide.active {
    display: block;
}

.review-wrap {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-xl);
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.review-cover {
    aspect-ratio: 1;
    border-radius: var(--radius-large);
    overflow: hidden;
}

.review-inner {
    padding: var(--spacing-lg);
}

.review-info {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
    font-style: italic;
}

.review-title h5 {
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.review-title div {
    color: var(--text-light);
    font-size: 0.875rem;
}

.slider-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.left-arrow,
.right-arrow {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.left-arrow:hover,
.right-arrow:hover {
    background-color: var(--text-color);
    transform: scale(1.1);
}

.slide-nav {
    display: flex;
    gap: var(--spacing-sm);
}

.slide-dot {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(45, 90, 39, 0.2);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    font-size: 0.875rem;
    font-weight: 500;
}

.slide-dot.active,
.slide-dot:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Articles Section */
.articles-section {
    padding: var(--spacing-xxxl) 0;
}

.section-wrap {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.articles-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.article-main-card {
    position: relative;
    cursor: pointer;
}

.article-block {
    aspect-ratio: 16/10;
    border-radius: var(--radius-large);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.article-title {
    padding: var(--spacing-md);
}

.article-top {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-light);
}

.article-line {
    width: 20px;
    height: 1px;
    background-color: var(--text-light);
}

.article-wrap {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.article-card {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    cursor: pointer;
    position: relative;
}

.article-card:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.article-cover {
    aspect-ratio: 4/3;
    border-radius: var(--radius-small);
    overflow: hidden;
}

.article-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-decoration: none;
}

/* CTA Section */
.cta-section {
    padding: var(--spacing-xxxl) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
}

.cta-wrap {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-outer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: center;
}

.cta-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: center;
}

.cta-title .caption {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(255,255,255,0.8);
}

.cta-info {
    font-size: 1.125rem;
    line-height: 1.6;
    color: rgba(255,255,255,0.9);
    max-width: 600px;
    margin: 0;
}

/* Simplified Background Image Approach */
.work-main-card, .work-card {
    background-color: #f5f5f5;
    border-radius: var(--radius-medium);
    overflow: hidden;
}

.work-main-card .work-main, 
.work-card .work-cover {
    position: relative;
    background: #f5f5f5;
}

/* Loading placeholder for background images */
.work-main::before,
.work-cover::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0.3;
    z-index: 1;
}

/* Hide loading spinner when background image loads */
.work-main[style*="background-image"]::before,
.work-cover[style*="background-image"]::before {
    display: none;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Image Loading and Error Handling */
.cover-image {
    transition: opacity 0.3s ease, transform var(--transition-slow);
}

.cover-image:not([src]),
.cover-image[src=""],
.cover-image[src*="undefined"] {
    opacity: 0;
    background-color: #f5f5f5;
}

.cover-image:not([src]):before,
.cover-image[src=""]:before,
.cover-image[src*="undefined"]:before {
    content: 'Image Loading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 0.875rem;
    z-index: 1;
}

/* Force proper image rendering */
.work-main,
.work-cover {
    background-color: #f5f5f5 !important;
    display: flex;
    align-items: center;
    justify-content: center;
}

.work-main .cover-image,
.work-cover .cover-image {
    flex-shrink: 0;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.work-main .cover-image[src],
.work-cover .cover-image[src] {
    opacity: 1;
}

/* Image Loading States */
.cover-image.loading {
    opacity: 0.5;
    background-color: #f5f5f5;
}

.cover-image.loaded {
    opacity: 1;
}

.cover-image.error {
    opacity: 0.3;
    background-color: #f5f5f5;
}

/* Loading animation */
@keyframes imageLoading {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.cover-image.loading {
    background: linear-gradient(90deg, #f5f5f5 0px, #e0e0e0 40px, #f5f5f5 80px);
    background-size: 200px;
    animation: imageLoading 1.5s infinite;
}

/* Ultimate Image Fitting Solution */
.work-main-card .work-main,
.work-card .work-cover {
    position: relative;
    overflow: hidden;
    background: #f5f5f5;
    display: block;
}

.work-main-card .work-main img,
.work-card .work-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

/* Hover effects */
.work-main-card:hover .work-main img,
.work-card:hover .work-cover img {
    transform: scale(1.05);
}

/* Ensure no image distortion */
.cover-image {
    max-width: none !important;
    max-height: none !important;
    min-width: 100% !important;
    min-height: 100% !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-wrap {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .work-wrapper {
        grid-template-columns: 1fr;
    }

    .work-last {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .work-last p {
        max-width: 100%;
    }

    .service-block {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .review-wrap {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .title-wrap {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }

    /* Work section responsive improvements */
    .work-main {
        height: 220px;
    }
    
    .work-content {
        padding: var(--spacing-md);
    }
    
    .work-content h6 {
        font-size: 1rem;
    }
    
    .work-card {
        padding: var(--spacing-sm);
    }
    
    .work-card .work-cover {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .best-inner {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .client-logos-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
        max-width: 600px;
    }

    .logo-card {
        width: 100px;
        height: 50px;
    }

    .work-card,
    .article-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .slider-navigation {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .best-inner {
        grid-template-columns: 1fr;
    }

    .hero-info {
        gap: var(--spacing-md);
    }

    .hero-data {
        font-size: 1rem;
    }

    .section-title h2 {
        font-size: clamp(1.5rem, 6vw, 2rem);
    }
}

/* Animation Classes */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Hover Effects */
/* Work Card Hover Effects with Background Images */
.work-main-card:hover .work-main {
    transform: scale(1.05);
}

.work-card:hover .work-cover {
    transform: scale(1.05);
}

/* Focus States */
.primary-button:focus,
.hero-button:focus,
.left-arrow:focus,
.right-arrow:focus,
.slide-dot:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== ARKIFY HEADER STYLES ===== */
.arkify-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.header-logo .logo-link {
    display: flex;
    align-items: center;
}

.main-logo {
    height: 40px;
    width: auto;
}

.header-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-dropdown {
    position: relative;
}

.nav-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--text-color);
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.nav-dropdown-btn:hover {
    color: var(--primary-color);
}

.nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-sm);
    min-width: 160px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.nav-dropdown:hover .nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: var(--spacing-sm);
    color: var(--text-color);
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.dropdown-link:hover {
    background-color: var(--background-light);
}

.header-cta-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.header-cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-color);
    transition: all 0.3s ease;
}

.mobile-navigation {
    display: none;
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid var(--border-color);
    z-index: 999;
}

.mobile-nav-content {
    padding: var(--spacing-lg);
}

.mobile-nav-menu {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.mobile-nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.mobile-cta-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    justify-content: center;
    margin-top: var(--spacing-md);
}

/* ===== MODERN FOOTER STYLES ===== */
.modern-footer {
    position: relative;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: var(--white);
    padding: 80px 0 40px;
    overflow: hidden;
}

.footer-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.footer-content {
    position: relative;
    z-index: 1;
}

.footer-main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 60px;
    margin-bottom: 60px;
}

/* Brand Section */
.footer-brand-section {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.footer-logo-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.footer-logo-link {
    display: inline-block;
    transition: transform 0.3s ease;
}

.footer-logo-link:hover {
    transform: translateY(-2px);
}

.footer-logo {
    height: 45px;
    width: auto;
    filter: brightness(1.1);
}

.footer-tagline {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    line-height: 1.6;
    max-width: 280px;
    margin: 0;
}

/* Newsletter Section */
.newsletter-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.newsletter-title {
    color: var(--white);
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.newsletter-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 0 0 20px 0;
    line-height: 1.5;
}

.newsletter-form {
    width: 100%;
}

.newsletter-input-group {
    display: flex;
    gap: 12px;
    align-items: stretch;
}

.newsletter-input {
    flex: 1;
    padding: 14px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    font-size: 14px;
    transition: all 0.3s ease;
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.newsletter-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 20px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.newsletter-button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

/* Navigation Section */
.footer-nav-section {
    display: flex;
    gap: 40px;
}

.footer-nav-column {
    flex: 1;
}

.footer-nav-title {
    color: var(--white);
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 20px 0;
    position: relative;
}

.footer-nav-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.footer-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-nav-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 0;
}

.footer-nav-link:hover {
    color: var(--white);
    padding-left: 8px;
}

.footer-nav-link::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.footer-nav-link:hover::before {
    width: 4px;
}

/* Contact Section */
.footer-contact-section {
    background: rgba(255, 255, 255, 0.03);
    padding: 30px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.footer-contact-title {
    color: var(--white);
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 25px 0;
}

.contact-info-grid {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.contact-info-item {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.contact-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.contact-details {
    flex: 1;
}

.contact-label {
    color: var(--white);
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    display: block;
}

.contact-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.contact-links {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: var(--primary-color);
}

/* Footer Bottom */
.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 40px;
    margin-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.footer-copyright {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.copyright-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 0;
}

.footer-legal-links {
    display: flex;
    gap: 20px;
    align-items: center;
}

.footer-legal-link {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.footer-legal-link:hover {
    color: var(--white);
}

.footer-social {
    display: flex;
    align-items: center;
    gap: 20px;
}

.social-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 600;
}

.social-links {
    display: flex;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

/* ===== RESPONSIVE STYLES FOR HEADER & FOOTER ===== */
@media (max-width: 768px) {
    .header-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-navigation.active {
        display: block;
    }

    /* Modern Footer Responsive */
    .modern-footer {
        padding: 60px 0 30px;
    }

    .footer-main-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 40px;
    }

    .footer-nav-section {
        flex-direction: column;
        gap: 30px;
    }

    .newsletter-section {
        padding: 25px;
    }

    .newsletter-input-group {
        flex-direction: column;
        gap: 12px;
    }

    .newsletter-button {
        justify-content: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 25px;
        text-align: center;
    }

    .footer-legal-links {
        justify-content: center;
        flex-wrap: wrap;
    }

    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-wrap {
        padding: var(--spacing-sm) 0;
    }

    .main-logo {
        height: 32px;
    }

    /* Modern Footer Small Mobile */
    .modern-footer {
        padding: 50px 0 25px;
    }

    .footer-main-grid {
        gap: 30px;
        margin-bottom: 30px;
    }

    .footer-brand-section {
        gap: 25px;
    }

    .newsletter-section,
    .footer-contact-section {
        padding: 20px;
    }

    .footer-nav-section {
        gap: 25px;
    }

    .footer-nav-title {
        font-size: 16px;
    }

    .footer-tagline {
        font-size: 15px;
    }

    .social-links {
        gap: 8px;
    }

    .social-link {
        width: 36px;
        height: 36px;
    }

    .footer-legal-links {
        gap: 15px;
        font-size: 12px;
    }

    /* Hero Text Slider Mobile */
    .hero-title {
        font-size: clamp(2.5rem, 5vw, 4rem);
    }

    .hero-data {
        font-size: clamp(0.9rem, 1.8vw, 1.1rem);
    }

    .slider-dots {
        gap: 8px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }
}

/* Print Styles */
@media print {
    .hero-button,
    .slider-navigation,
    .client-logos-section {
        display: none;
    }

    .hero-section {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }

    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
    }
}

/* Hero title sliding animation */
@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ==========================================================================
   ABOUT PAGE STYLES (Inspired by Conc Template)
   ========================================================================== */

/* About Hero Section */
.about-hero {
    position: relative;
    padding: 120px 0 80px;
    overflow: hidden;
}

.breadcrumb {
    margin-bottom: 2rem;
}

.breadcrumb-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--accent-color);
    letter-spacing: 2px;
    text-transform: uppercase;
}

.about-hero .hero-title {
    font-size: 4rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.1;
    text-shadow: none;
}

.about-hero .hero-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.3;
    max-width: 700px;
    text-shadow: none;
}

.about-hero .hero-description {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    max-width: 600px;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 4rem;
    margin-bottom: 3rem;
}

.hero-stats .stat-item {
    text-align: left;
}

.hero-stats .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.hero-stats .stat-label {
    font-size: 1rem;
    color: var(--text-light);
    font-weight: 500;
}

.about-hero .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.about-images {
    position: absolute;
    right: 5%;
    top: 50%;
    transform: translateY(-50%);
    width: 45%;
    height: 500px;
    display: flex;
    gap: 1rem;
}

.about-image-left,
.about-image-right {
    flex: 1;
    border-radius: 12px;
    overflow: hidden;
}

.about-image-left {
    margin-top: 2rem;
}

.about-image-right {
    margin-bottom: 2rem;
}

.about-images img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Services Grid Section */
.services-grid {
    background-color: var(--bg-light);
}

.services-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.service-card {
    background: var(--white);
    padding: 3rem 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-medium);
    border: 1px solid var(--border-color);
    text-align: center;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, var(--accent-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--accent-color);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.service-link:hover {
    color: var(--text-color);
    transform: translateX(5px);
}

.service-link svg {
    transition: transform var(--transition-fast);
}

.service-link:hover svg {
    transform: translateX(3px);
}

/* Service Details Page */
.service-overview {
    background-color: var(--white);
}

.service-overview-grid {
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 5rem;
    align-items: start;
}

.service-content .section-header {
    margin-bottom: 2rem;
}

.service-description {
    margin-bottom: 3rem;
}

.service-description p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
    color: var(--text-light);
}

.service-features h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.features-list {
    list-style: none;
    padding: 0;
}

.features-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 2rem;
    color: var(--text-light);
}

.features-list li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

.service-images {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.service-image-main {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.service-image-main img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.service-image-secondary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.service-image-secondary img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 12px;
}

/* Service Process Section */
.service-process {
    background-color: var(--bg-light);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-color), #ff6b35);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.step-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* Related Projects Section */
.related-projects {
    background-color: var(--white);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.project-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-medium);
    border: 1px solid var(--border-color);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.project-image {
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-medium);
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-content {
    padding: 2rem;
}

.project-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.project-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-link {
    color: var(--accent-color);
    font-weight: 600;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.project-link:hover {
    color: var(--text-color);
}

/* Service Hero Footer */
.service-hero-footer {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.service-hero-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
    z-index: 1;
}

.service-hero-footer .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
    max-width: 600px;
    padding: 0 var(--spacing-md);
}

.service-hero-footer .hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.service-hero-footer .hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Company Values Section */
.company-values {
    background-color: var(--white);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.value-card {
    text-align: center;
    padding: 3rem 2rem;
    border-radius: 20px;
    background: var(--white);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all var(--transition-medium);
    border: 1px solid var(--border-color);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.value-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, var(--accent-color), #ff6b35);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.value-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.value-description {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 1rem;
}

/* Company History Timeline - Two Column Layout */
.company-history {
    padding: 100px 0;
    background-color: var(--light-accent);
}

.company-history .container {
    display: grid;
    grid-template-columns: 1.2fr 1fr; /* Give more space to the left column with image */
    gap: 6rem;
    align-items: start;
}

.history-left {
    position: sticky;
    top: 2rem;
}

.section-header {
    text-align: left;
    margin-bottom: 3rem;
}

.section-caption {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--accent-color);
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    display: block;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1.2;
    margin: 0;
}

.history-main-image {
    border-radius: 20px;
    overflow: hidden;
    aspect-ratio: 4/5;
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
    max-width: 500px; /* Ensure good size */
    width: 100%;
}

.history-main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.history-main-image:hover img {
    transform: scale(1.05);
}

.timeline {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.timeline-item {
    padding: 2rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline-year {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 1rem 0;
}

.timeline-description {
    font-size: 1rem;
    color: var(--text-light);
    line-height: 1.7;
    margin: 0;
}

.timeline-arrow {
    color: var(--accent-color);
    opacity: 0.7;
}

.timeline-year {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 1rem 0;
}

.timeline-description {
    font-size: 1rem;
    color: var(--text-light);
    line-height: 1.7;
    margin: 0;
}

/* FAQ Section */
.faq-section {
    padding: 100px 0;
    background-color: var(--secondary-color);
}

.faq-section .container {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 4rem;
    align-items: start;
}

.faq-section .section-header {
    margin-bottom: 0;
    text-align: left;
}

.faq-contact {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-top: 2rem;
}

.faq-contact h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.faq-contact p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.faq-item {
    background: white;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.faq-item:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.faq-item:last-child {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    border-bottom: none;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.faq-question:hover {
    background-color: #fafafa;
}

.faq-question h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    flex: 1;
}

.faq-toggle {
    color: var(--text-light);
    transition: transform 0.3s ease;
    font-size: 1.5rem;
    line-height: 1;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
    color: var(--accent-color);
}

.faq-answer {
    padding: 0 2.5rem 2rem;
    display: none;
    border-top: 1px solid #f0f0f0;
    margin-top: -1px;
}

.faq-item.active .faq-answer {
    display: block;
    animation: fadeInDown 0.3s ease;
}

.faq-answer p {
    color: var(--text-light);
    line-height: 1.7;
    margin: 0;
    padding-top: 1rem;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* About CTA Section */
.about-cta {
    padding: 100px 0;
    background-color: var(--secondary-color);
}

.cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 4rem;
}

.cta-text {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.cta-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1.2;
    margin: 0;
    max-width: 600px;
}

.cta-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-stats .stat-item {
    text-align: center;
    padding: 2rem 1.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.cta-stats .stat-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.cta-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.cta-stats .stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
    line-height: 1.4;
}

.cta-image {
    border-radius: 16px;
    overflow: hidden;
    aspect-ratio: 16/9;
    margin-top: 3rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.cta-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.cta-image:hover img {
    transform: scale(1.05);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.btn-secondary:hover {
    background-color: transparent;
    color: var(--accent-color);
    transform: translateY(-2px);
}

/* Utility Classes */
.bg-light {
    background-color: var(--secondary-color);
}

.section-padding {
    padding: 100px 0;
}

/* About Page Responsive Styles */
@media (max-width: 1024px) {
    .about-hero {
        padding: 100px 0 60px;
    }

    .about-images {
        position: static;
        width: 100%;
        height: 300px;
        margin-top: 3rem;
        transform: none;
    }

    .timeline-item {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .timeline-item:nth-child(even) {
        grid-template-columns: 1fr;
    }

    .timeline-item:nth-child(even) .timeline-content {
        order: 0;
        text-align: center;
    }

    .faq-section .container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .faq-section .section-header {
        text-align: center;
    }

    .cta-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .company-history .container {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .history-left {
        position: static;
    }

    .section-header {
        text-align: center;
    }

    .history-main-image {
        aspect-ratio: 16/10;
        margin-bottom: 2rem;
        max-width: 100%; /* Take full width on mobile */
    }
}

@media (max-width: 768px) {
    .page-hero .hero-title {
        font-size: 2.5rem;
    }

    .page-hero {
        min-height: 50vh;
        margin-top: 70px;
    }

    .about-hero .hero-title {
        font-size: 2.5rem;
    }

    .about-hero .hero-subtitle {
        font-size: 1.25rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .about-images {
        flex-direction: column;
        height: 400px;
    }

    .about-image-left,
    .about-image-right {
        margin: 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .timeline {
        gap: 3rem;
    }

    .timeline-item {
        gap: 1.5rem;
    }

    .timeline-image {
        aspect-ratio: 16/10;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-stats {
        flex-direction: row;
        gap: 1rem;
    }

    .faq-question {
        padding: 1rem 1.5rem;
    }

    .faq-answer {
        padding: 0 1.5rem 1rem;
    }
}

@media (max-width: 480px) {
    .about-hero {
        padding: 80px 0 40px;
    }

    .about-hero .hero-title {
        font-size: 2rem;
    }

    .about-hero .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .hero-stats .stat-number {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .timeline-title {
        font-size: 1.25rem;
    }

    .cta-title {
        font-size: 1.75rem;
    }

    .cta-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .faq-question h4 {
        font-size: 1rem;
    }

    .services-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-overview-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .service-image-secondary {
        grid-template-columns: 1fr;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-hero-footer .hero-title {
        font-size: 2.5rem;
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
