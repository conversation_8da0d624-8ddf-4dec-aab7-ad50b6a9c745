/* ===== CLEAN FEATURED WORK SECTION CSS ===== */

.work-outer-main {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.work-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

/* Main Project Card */
.work-main-card {
    position: relative;
    border-radius: var(--radius-large);
    overflow: hidden;
    cursor: pointer;
}

.work-main {
    height: 400px;
    width: 100%;
    background-color: #f5f5f5;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: var(--radius-large);
    transition: transform 0.3s ease;
    position: relative;
}

.work-main-card:hover .work-main {
    transform: scale(1.02);
}

/* Secondary Project Cards */
.work-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.work-card {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.8);
    align-items: flex-start;
    min-height: 120px;
}

.work-card:hover {
    background-color: #F5F5F5;
    transform: translateY(-2px);
}

.work-cover {
    height: 120px;
    width: 180px;
    background-color: #f5f5f5;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    border-radius: 8px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.work-card:hover .work-cover {
    transform: scale(1.05);
}

/* Content Styling */
.work-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    background: linear-gradient(transparent, rgba(0,0,0,0.85));
    color: white;
    border-radius: 0 0 var(--radius-large) var(--radius-large);
}

.work-card .work-content {
    position: static;
    background: transparent;
    color: #333;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.work-content h6 {
    margin: 0.25rem 0 0 0;
    font-size: 1rem;
    line-height: 1.4;
    font-weight: 600;
}

.work-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.work-title > div:first-child {
    color: #666;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
}

.work-mini-card {
    background-color: rgba(255,255,255,0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    backdrop-filter: blur(10px);
}

.work-card .work-mini-card {
    background-color: #E67E22;
    color: white;
    backdrop-filter: none;
}

.body-small {
    font-size: 0.75rem;
    font-weight: 500;
}

.work-last {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background-color: #F5F5F5;
    border-radius: 16px;
    margin-top: 2rem;
}

.work-last p {
    max-width: 60%;
    margin: 0;
}

/* Ensure background images fit properly */
.work-main[style*="background-image"],
.work-cover[style*="background-image"] {
    background-attachment: scroll;
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
}

/* Add subtle overlay for better text readability on main card */
.work-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        transparent 60%,
        rgba(0,0,0,0.1) 70%,
        rgba(0,0,0,0.4) 100%
    );
    border-radius: var(--radius-large);
    pointer-events: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .work-wrapper {
        grid-template-columns: 1fr;
    }

    .work-last {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .work-last p {
        max-width: 100%;
    }

    .work-main {
        height: 250px;
    }
    
    .work-cover {
        height: 120px;
    }
    
    .work-card {
        flex-direction: column;
        gap: 0.75rem;
        text-align: left;
        align-items: stretch;
    }
    
    .work-card .work-cover {
        height: 200px;
        width: 100%;
    }
    
    .work-card .work-content {
        padding-top: 0.5rem;
    }
    
    .work-title > div:first-child {
        max-width: none;
    }
}

@media (max-width: 768px) {
    .work-main {
        height: 220px;
    }
    
    .work-content {
        padding: 1rem;
    }
    
    .work-content h6 {
        font-size: 1rem;
    }
    
    .work-card {
        flex-direction: column;
        padding: 0.75rem;
        gap: 0.5rem;
    }
    
    .work-card .work-cover {
        height: 180px;
        width: 100%;
    }
    
    .work-title > div:first-child {
        max-width: none;
    }
}
