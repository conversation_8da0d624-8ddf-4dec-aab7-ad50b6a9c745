<?php
/**
 * Services Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication - session already started in functions.php
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle form submissions
$message = '';
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_service':
                $title = sanitizeInput($_POST['title']);
                $slug = sanitizeInput($_POST['slug']);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Allow HTML
                $icon = sanitizeInput($_POST['icon']);
                $sort_order = intval($_POST['sort_order']);
                
                $db = Database::getConnection();
                $stmt = $db->prepare("INSERT INTO services (title, slug, description, content, icon, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                if ($stmt->execute([$title, $slug, $description, $content, $icon, $sort_order])) {
                    $message = 'Service added successfully!';
                } else {
                    $message = 'Error adding service.';
                }
                break;
                
            case 'update_service':
                $id = intval($_POST['id']);
                $title = sanitizeInput($_POST['title']);
                $slug = sanitizeInput($_POST['slug']);
                $description = sanitizeInput($_POST['description']);
                $content = $_POST['content']; // Allow HTML
                $icon = sanitizeInput($_POST['icon']);
                $sort_order = intval($_POST['sort_order']);
                $active = isset($_POST['active']) ? 1 : 0;
                
                $db = Database::getConnection();
                $stmt = $db->prepare("UPDATE services SET title = ?, slug = ?, description = ?, content = ?, icon = ?, sort_order = ?, active = ? WHERE id = ?");
                if ($stmt->execute([$title, $slug, $description, $content, $icon, $sort_order, $active, $id])) {
                    $message = 'Service updated successfully!';
                } else {
                    $message = 'Error updating service.';
                }
                break;
                
            case 'delete_service':
                $id = intval($_POST['id']);
                $db = Database::getConnection();
                $stmt = $db->prepare("DELETE FROM services WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Service deleted successfully!';
                } else {
                    $message = 'Error deleting service.';
                }
                break;
        }
    }
}

// Get all services
$db = Database::getConnection();
$stmt = $db->query("SELECT * FROM services ORDER BY sort_order ASC");
$services = $stmt->fetchAll();

// Get service for editing
$edit_service = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $db->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$edit_id]);
    $edit_service = $stmt->fetch();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services Management - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .admin-header {
            background: #34495e;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #2c3e50;
            padding: 0;
            display: flex;
            overflow-x: auto;
        }
        
        .admin-nav a {
            color: #bdc3c7;
            text-decoration: none;
            padding: 1rem 1.5rem;
            white-space: nowrap;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #34495e;
            color: white;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            background: #3498db;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .message {
            background: #d5edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }
        
        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .services-table th,
        .services-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .services-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .services-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-active {
            color: #27ae60;
            font-weight: 600;
        }
        
        .status-inactive {
            color: #e74c3c;
            font-weight: 600;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Services Management</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="services.php" class="active">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <!-- Add/Edit Service Form -->
        <div class="admin-card">
            <div class="admin-card-header">
                <?php echo $edit_service ? 'Edit Service' : 'Add New Service'; ?>
            </div>
            <div class="admin-card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="<?php echo $edit_service ? 'update_service' : 'add_service'; ?>">
                    <?php if ($edit_service): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_service['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">Service Title</label>
                            <input type="text" id="title" name="title" value="<?php echo $edit_service ? htmlspecialchars($edit_service['title']) : ''; ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" id="slug" name="slug" value="<?php echo $edit_service ? htmlspecialchars($edit_service['slug']) : ''; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Short Description</label>
                        <textarea id="description" name="description" required><?php echo $edit_service ? htmlspecialchars($edit_service['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="content">Full Content (HTML allowed)</label>
                        <textarea id="content" name="content" style="height: 200px;"><?php echo $edit_service ? htmlspecialchars($edit_service['content']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="icon">Icon (SVG or Font Awesome class)</label>
                            <input type="text" id="icon" name="icon" value="<?php echo $edit_service ? htmlspecialchars($edit_service['icon']) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="sort_order">Sort Order</label>
                            <input type="number" id="sort_order" name="sort_order" value="<?php echo $edit_service ? $edit_service['sort_order'] : count($services) + 1; ?>" required>
                        </div>
                    </div>
                    
                    <?php if ($edit_service): ?>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="active" name="active" <?php echo $edit_service['active'] ? 'checked' : ''; ?>>
                                <label for="active">Active</label>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <button type="submit" class="btn btn-success">
                        <?php echo $edit_service ? 'Update Service' : 'Add Service'; ?>
                    </button>
                    
                    <?php if ($edit_service): ?>
                        <a href="services.php" class="btn">Cancel</a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        
        <!-- Services List -->
        <div class="admin-card">
            <div class="admin-card-header">
                Existing Services (<?php echo count($services); ?>/9)
            </div>
            <div class="admin-card-body">
                <?php if (empty($services)): ?>
                    <p>No services found. Add your first service above.</p>
                <?php else: ?>
                    <table class="services-table">
                        <thead>
                            <tr>
                                <th>Order</th>
                                <th>Title</th>
                                <th>Slug</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($services as $service): ?>
                                <tr>
                                    <td><?php echo $service['sort_order']; ?></td>
                                    <td><?php echo htmlspecialchars($service['title']); ?></td>
                                    <td><?php echo htmlspecialchars($service['slug']); ?></td>
                                    <td>
                                        <span class="<?php echo $service['active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $service['active'] ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="services.php?edit=<?php echo $service['id']; ?>" class="btn" style="padding: 0.5rem 1rem; font-size: 0.9rem;">Edit</a>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this service?');">
                                            <input type="hidden" name="action" value="delete_service">
                                            <input type="hidden" name="id" value="<?php echo $service['id']; ?>">
                                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem 1rem; font-size: 0.9rem;">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
